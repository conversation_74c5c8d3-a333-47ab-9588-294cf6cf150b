#!/usr/bin/env node

/**
 * Dedicated GitHub Webhook Server
 * Runs on port 9000 for GitHub webhook connections
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const webhookRoutes = require('../routes/webhook');

const app = express();
const PORT = process.env.WEBHOOK_PORT || 9000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Allow webhook requests
  crossOriginEmbedderPolicy: false
}));

// CORS for webhook monitoring
app.use(cors({
  origin: [
    'https://streamdb.online',
    'https://www.streamdb.online',
    'http://localhost:3001'
  ],
  credentials: true
}));

// Logging
app.use(morgan('combined'));

// Basic health check
app.get('/health', (req, res) => {
  res.json({
    service: 'GitHub Webhook Server',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Webhook routes
app.use('/api/webhook', webhookRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'This is the GitHub webhook server. Use /api/webhook endpoints.',
    available_endpoints: [
      'GET /health',
      'POST /api/webhook/github',
      'GET /api/webhook/status',
      'GET /api/webhook/test'
    ]
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Webhook server error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Webhook processing failed'
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 GitHub Webhook Server running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📡 Webhook endpoint: http://YOUR_IP:${PORT}/api/webhook/github`);
  console.log(`🌍 Listening on all interfaces for GitHub webhooks`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down webhook server gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down webhook server gracefully');
  process.exit(0);
});
