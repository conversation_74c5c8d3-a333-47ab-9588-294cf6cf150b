
import { Link } from "react-router-dom";
import { MediaItem } from "@/types/media";
import { scrollToTop } from "@/utils/scrollToTop";
import SafeImage from "@/components/SafeImage";

export default function CardGrid({ items }: { items: MediaItem[] }) {
  // Safety check for items array
  if (!items || !Array.isArray(items)) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4 md:gap-6">
        <div className="col-span-full text-center text-muted-foreground py-8">
          <div className="text-4xl mb-2">🎬</div>
          <p>No content available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4 md:gap-6">
      {items.map(item => {
        // Safety checks for item properties
        if (!item || !item.id) {
          return null;
        }

        const safeItem = {
          id: item.id,
          title: item.title || 'Untitled',
          image: item.image || item.posterUrl || '/placeholder-image.jpg',
          genres: Array.isArray(item.genres) ? item.genres : [],
          year: item.year || 'Unknown'
        };

        return (
          <Link
            to={`/content/${safeItem.id}`}
            key={safeItem.id}
            onClick={scrollToTop}
            className="card-grid-item group bg-card shadow-lg hover:scale-[1.04] transition-transform duration-200 hover:ring-2 hover:ring-primary/60 flex flex-col rounded-2xl overflow-hidden"
          >
            {/* Poster image */}
            <div className="relative w-full" style={{ aspectRatio: "2/3", background: "#191d25" }}>
              <SafeImage
                src={safeItem.image}
                alt={safeItem.title}
                className="w-full h-full object-cover bg-[#191d25] rounded-none"
                style={{
                  aspectRatio: "2/3",
                  display: "block"
                }}
                placeholder={
                  <div className="w-full h-full flex items-center justify-center bg-muted">
                    <div className="text-center text-muted-foreground">
                      <div className="text-3xl mb-2">🎬</div>
                      <div className="text-xs">Loading...</div>
                    </div>
                  </div>
                }
              />
            </div>
            {/* Title */}
            <span
              className="card-title"
              title={safeItem.title}
            >
              {safeItem.title}
            </span>
            {/* Genres & year, modern layout */}
            <div
              className="flex flex-col gap-1.5 items-center justify-center px-2 pb-2 mb-1"
              style={{
                background: "none",
                borderTop: "1px solid rgba(100,100,100,0.10)"
              }}
            >
              {/* Updated card-genres uses new tan color with safe array handling */}
              <span className="card-genres">
                {safeItem.genres.length > 0 ? safeItem.genres.join(", ") : "No genres"}
              </span>
              <span className="card-year">{safeItem.year}</span>
            </div>
          </Link>
        );
      }).filter(Boolean)}
    </div>
  );
}

