import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Safe localStorage wrapper to prevent errors and handle browser extension conflicts
const safeLocalStorage = {
  isAvailable: false,
  extensionConflictDetected: false,
  fallbackStorage: new Map<string, string>(),

  init() {
    try {
      if (typeof localStorage !== 'undefined' && localStorage) {
        // Test localStorage functionality safely with more robust testing
        const testKey = '__localStorage_test_' + Date.now();
        const testValue = 'test_value_' + Math.random();

        // Attempt to set and retrieve a value
        const setResult = localStorage.setItem(testKey, testValue);

        // Check if setItem returned undefined (extension conflict indicator)
        if (setResult === undefined) {
          console.warn('localStorage.setItem returned undefined - browser extension conflict detected');
          this.extensionConflictDetected = true;
          this.isAvailable = false;
          return;
        }

        const retrieved = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);

        if (retrieved === testValue) {
          this.isAvailable = true;
          this.extensionConflictDetected = false;
        } else {
          throw new Error('localStorage test failed - value mismatch');
        }
      } else {
        console.warn('localStorage is not available in this environment');
        this.isAvailable = false;
      }
    } catch (error) {
      console.warn('localStorage test failed:', error);
      this.isAvailable = false;

      // Check for specific extension conflict patterns
      if (error && (error.toString().includes('undefined') || error.toString().includes('extension'))) {
        this.extensionConflictDetected = true;
        console.warn('Browser extension conflict detected with localStorage');
      }
    }
  },

  getItem(key: string): string | null {
    if (!this.isAvailable) {
      // Use fallback storage
      return this.fallbackStorage.get(key) || null;
    }

    try {
      const result = localStorage.getItem(key);
      // Handle extension conflicts that return undefined instead of null
      return result === undefined ? null : result;
    } catch (error) {
      console.warn('localStorage.getItem failed:', error);
      return this.fallbackStorage.get(key) || null;
    }
  },

  setItem(key: string, value: string): boolean {
    if (!this.isAvailable) {
      // Use fallback storage
      this.fallbackStorage.set(key, value);
      return true;
    }

    try {
      const result = localStorage.setItem(key, value);

      // Check if setItem returned undefined (extension conflict)
      if (result === undefined) {
        console.warn('localStorage.setItem returned undefined, using fallback');
        this.fallbackStorage.set(key, value);
        return true;
      }

      return true;
    } catch (error) {
      console.warn('localStorage.setItem failed:', error);
      this.fallbackStorage.set(key, value);
      return true; // Still return true since we used fallback
    }
  },

  removeItem(key: string): boolean {
    if (!this.isAvailable) {
      // Use fallback storage
      this.fallbackStorage.delete(key);
      return true;
    }

    try {
      localStorage.removeItem(key);
      this.fallbackStorage.delete(key); // Also remove from fallback
      return true;
    } catch (error) {
      console.warn('localStorage.removeItem failed:', error);
      this.fallbackStorage.delete(key);
      return true;
    }
  },

  // Additional utility methods
  clear(): boolean {
    this.fallbackStorage.clear();

    if (!this.isAvailable) return true;

    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.warn('localStorage.clear failed:', error);
      return true;
    }
  },

  getStatus(): { available: boolean; extensionConflict: boolean; fallbackItems: number } {
    return {
      available: this.isAvailable,
      extensionConflict: this.extensionConflictDetected,
      fallbackItems: this.fallbackStorage.size
    };
  }
};

// Initialize safe localStorage
safeLocalStorage.init();

// Make it globally available for components that need it
(window as any).safeLocalStorage = safeLocalStorage;

// Production error detection
if (import.meta.env.PROD) {
  window.addEventListener('error', (e) => {
    console.error('Production Error:', e.error);
    console.error('Error details:', {
      message: e.message,
      filename: e.filename,
      lineno: e.lineno,
      colno: e.colno,
      stack: e.error?.stack
    });
  });

  window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    console.error('Promise rejection details:', {
      reason: e.reason,
      stack: e.reason?.stack
    });
  });
}

const rootElement = document.getElementById("root");

// Remove loading indicator if it exists
const loadingIndicator = document.getElementById("loading-indicator");
if (loadingIndicator) {
  loadingIndicator.remove();
}

if (rootElement) {
  try {
    createRoot(rootElement).render(<App />);
  } catch (error) {
    console.error('Failed to render React app:', error);
    // Fallback display
    rootElement.innerHTML = `
      <div style="padding: 20px; color: #e6cb8e; background: #0a0a0a; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
        <div style="text-align: center; max-width: 500px;">
          <h1>StreamDB</h1>
          <p>Application failed to load. Please refresh the page.</p>
          <p style="font-size: 12px; opacity: 0.7;">Error: ${error instanceof Error ? error.message : 'Unknown error'}</p>
          <button onclick="window.location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #e6cb8e; color: #0a0a0a; border: none; border-radius: 5px; cursor: pointer;">
            Reload Page
          </button>
        </div>
      </div>
    `;
  }
} else {
  // Create fallback if root element not found
  document.body.innerHTML = `
    <div style="padding: 20px; color: #e6cb8e; background: #0a0a0a; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
      <div style="text-align: center; max-width: 500px;">
        <h1>StreamDB</h1>
        <p>Application failed to initialize. Please refresh the page.</p>
        <button onclick="window.location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #e6cb8e; color: #0a0a0a; border: none; border-radius: 5px; cursor: pointer;">
          Reload Page
        </button>
      </div>
    </div>
  `;
}
