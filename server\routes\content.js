const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const db = require('../config/database');
const { authenticateToken, requireModerator, logAdminAction, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Validation rules for content creation/update
const contentValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title is required and must be less than 255 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Description must be less than 5000 characters'),
  body('year')
    .isInt({ min: 1900, max: new Date().getFullYear() + 5 })
    .withMessage('Year must be a valid year'),
  body('type')
    .isIn(['movie', 'series', 'requested'])
    .withMessage('Type must be movie, series, or requested'),
  body('category_id')
    .optional()
    .isInt()
    .withMessage('Category ID must be a valid integer'),
  body('genres')
    .optional()
    .isArray()
    .withMessage('Genres must be an array'),
  body('languages')
    .optional()
    .isArray()
    .withMessage('Languages must be an array'),
  body('quality')
    .optional()
    .isArray()
    .withMessage('Quality must be an array'),
  body('audioTracks')
    .optional()
    .isArray()
    .withMessage('Audio tracks must be an array'),
  body('imdb_rating')
    .optional()
    .isFloat({ min: 0, max: 10 })
    .withMessage('IMDB rating must be between 0 and 10'),
  body('is_published')
    .optional()
    .isBoolean()
    .withMessage('Published status must be boolean'),
  body('is_featured')
    .optional()
    .isBoolean()
    .withMessage('Featured status must be boolean'),
  body('add_to_carousel')
    .optional()
    .isBoolean()
    .withMessage('Carousel status must be boolean')
];

// Simple test endpoint
router.get('/test', async (req, res) => {
  try {
    const result = await db.execute('SELECT COUNT(*) as count FROM content WHERE is_published = 1');
    res.json({ success: true, count: result[0].count });
  } catch (error) {
    console.error('Test endpoint error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get all content with filtering and pagination
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('type').optional().isIn(['movie', 'series', 'requested']).withMessage('Invalid content type'),
  query('category').optional().isString().withMessage('Category must be a string'),
  query('genre').optional().isString().withMessage('Genre must be a string'),
  query('language').optional().isString().withMessage('Language must be a string'),
  query('year').optional().isInt().withMessage('Year must be an integer'),
  query('featured').optional().isBoolean().withMessage('Featured must be boolean'),
  query('published').optional().isBoolean().withMessage('Published must be boolean'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('sortBy').optional().isIn(['title', 'year', 'created_at', 'updated_at', 'imdb_rating']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC')
], optionalAuth, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Build filters with proper type handling
    const filters = {
      type: req.query.type,
      category: req.query.category,
      genre: req.query.genre,
      language: req.query.language,
      year: req.query.year ? parseInt(req.query.year) : undefined,
      featured: req.query.featured !== undefined ? (req.query.featured === 'true' || req.query.featured === true) : undefined,
      published: req.query.published !== undefined ? (req.query.published === 'true' || req.query.published === true) : undefined,
      search: req.query.search,
      sortBy: req.query.sortBy || 'created_at',
      sortOrder: req.query.sortOrder || 'DESC',
      limit,
      offset
    };

    // If user is not authenticated or not admin/moderator, only show published content
    if (!req.user || !['admin', 'moderator'].includes(req.user.role)) {
      filters.published = true;
    }

    // If no published filter is set and user is not admin, default to published only
    if (filters.published === undefined && (!req.user || !['admin', 'moderator'].includes(req.user.role))) {
      filters.published = true;
    }

    // Simplified content query for now to avoid SQL parameter issues
    let query = `
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM content c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_published = 1
    `;

    const params = [];

    if (filters.type) {
      query += ' AND c.type = ?';
      params.push(filters.type);
    }

    if (filters.category) {
      query += ' AND cat.slug = ?';
      params.push(filters.category);
    }

    query += ` ORDER BY c.${filters.sortBy} ${filters.sortOrder}`;

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(filters.limit);
    }

    const content = await db.execute(query, params);

    // Get total count for pagination
    const countFilters = { ...filters };
    delete countFilters.limit;
    delete countFilters.offset;
    delete countFilters.sortBy;
    delete countFilters.sortOrder;
    
    const totalContent = await db.searchContent(countFilters);
    const totalCount = totalContent.length;
    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      success: true,
      data: content,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      filters: {
        ...filters,
        totalResults: content.length
      }
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Get single content item with all relations
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const contentId = req.params.id;
    const content = await db.getContentWithRelations(contentId);

    if (!content) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // If content is not published and user is not admin/moderator, deny access
    if (!content.is_published && (!req.user || !['admin', 'moderator'].includes(req.user.role))) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // Get episodes if it's a series
    if (content.type === 'series' && content.seasons) {
      for (const season of content.seasons) {
        season.episodes = await db.getSeasonEpisodes(season.id);
      }
    }

    res.json({
      success: true,
      data: content
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Create new content (admin/moderator only)
router.post('/', authenticateToken, requireModerator, contentValidation, logAdminAction('content_create'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const contentId = uuidv4();
    const {
      title, description, year, type, category_id, image, cover_image,
      tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
      imdb_rating, runtime, studio, tags, trailer, subtitle_url,
      is_published = false, is_featured = false, add_to_carousel = false,
      total_seasons = 0, total_episodes = 0,
      genres = [], languages = [], quality = [], audioTracks = []
    } = req.body;

    // Start transaction
    const queries = [
      {
        query: `
          INSERT INTO content (
            id, title, description, year, type, category_id, image, cover_image,
            tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
            imdb_rating, runtime, studio, tags, trailer, subtitle_url,
            is_published, is_featured, add_to_carousel, total_seasons, total_episodes,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `,
        params: [
          contentId, title, description, year, type, category_id, image, cover_image,
          tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
          imdb_rating, runtime, studio, tags, trailer, subtitle_url,
          is_published, is_featured, add_to_carousel, total_seasons, total_episodes
        ]
      }
    ];

    // Add genre relations
    if (genres && genres.length > 0) {
      for (const genreId of genres) {
        queries.push({
          query: 'INSERT INTO content_genres (content_id, genre_id) VALUES (?, ?)',
          params: [contentId, genreId]
        });
      }
    }

    // Add language relations
    if (languages && languages.length > 0) {
      for (const languageId of languages) {
        queries.push({
          query: 'INSERT INTO content_languages (content_id, language_id) VALUES (?, ?)',
          params: [contentId, languageId]
        });
      }
    }

    // Add quality relations
    if (quality && quality.length > 0) {
      for (const qualityId of quality) {
        queries.push({
          query: 'INSERT INTO content_quality (content_id, quality_id) VALUES (?, ?)',
          params: [contentId, qualityId]
        });
      }
    }

    // Add audio track relations
    if (audioTracks && audioTracks.length > 0) {
      for (const audioId of audioTracks) {
        queries.push({
          query: 'INSERT INTO content_audio (content_id, audio_id) VALUES (?, ?)',
          params: [contentId, audioId]
        });
      }
    }

    await db.executeTransaction(queries);

    // Fetch the created content with relations
    const createdContent = await db.getContentWithRelations(contentId);

    res.status(201).json({
      success: true,
      message: 'Content created successfully',
      data: createdContent
    });

  } catch (error) {
    console.error('Error creating content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create content'
    });
  }
});

// Update content (admin/moderator only)
router.put('/:id', authenticateToken, requireModerator, contentValidation, logAdminAction('content_update'), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const contentId = req.params.id;
    const {
      title, description, year, type, category_id, image, cover_image,
      tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
      imdb_rating, runtime, studio, tags, trailer, subtitle_url,
      is_published, is_featured, add_to_carousel, total_seasons, total_episodes,
      genres = [], languages = [], quality = [], audioTracks = []
    } = req.body;

    // Check if content exists
    const existingContent = await db.execute('SELECT id FROM content WHERE id = ?', [contentId]);
    if (existingContent.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // Start transaction
    const queries = [
      {
        query: `
          UPDATE content SET
            title = ?, description = ?, year = ?, type = ?, category_id = ?,
            image = ?, cover_image = ?, tmdb_id = ?, poster_url = ?, thumbnail_url = ?,
            video_links = ?, secure_video_links = ?, imdb_rating = ?, runtime = ?,
            studio = ?, tags = ?, trailer = ?, subtitle_url = ?, is_published = ?,
            is_featured = ?, add_to_carousel = ?, total_seasons = ?, total_episodes = ?,
            updated_at = NOW()
          WHERE id = ?
        `,
        params: [
          title, description, year, type, category_id, image, cover_image,
          tmdb_id, poster_url, thumbnail_url, video_links, secure_video_links,
          imdb_rating, runtime, studio, tags, trailer, subtitle_url,
          is_published, is_featured, add_to_carousel, total_seasons, total_episodes,
          contentId
        ]
      },
      // Clear existing relations
      { query: 'DELETE FROM content_genres WHERE content_id = ?', params: [contentId] },
      { query: 'DELETE FROM content_languages WHERE content_id = ?', params: [contentId] },
      { query: 'DELETE FROM content_quality WHERE content_id = ?', params: [contentId] },
      { query: 'DELETE FROM content_audio WHERE content_id = ?', params: [contentId] }
    ];

    // Add new genre relations
    if (genres && genres.length > 0) {
      for (const genreId of genres) {
        queries.push({
          query: 'INSERT INTO content_genres (content_id, genre_id) VALUES (?, ?)',
          params: [contentId, genreId]
        });
      }
    }

    // Add new language relations
    if (languages && languages.length > 0) {
      for (const languageId of languages) {
        queries.push({
          query: 'INSERT INTO content_languages (content_id, language_id) VALUES (?, ?)',
          params: [contentId, languageId]
        });
      }
    }

    // Add new quality relations
    if (quality && quality.length > 0) {
      for (const qualityId of quality) {
        queries.push({
          query: 'INSERT INTO content_quality (content_id, quality_id) VALUES (?, ?)',
          params: [contentId, qualityId]
        });
      }
    }

    // Add new audio track relations
    if (audioTracks && audioTracks.length > 0) {
      for (const audioId of audioTracks) {
        queries.push({
          query: 'INSERT INTO content_audio (content_id, audio_id) VALUES (?, ?)',
          params: [contentId, audioId]
        });
      }
    }

    await db.executeTransaction(queries);

    // Fetch the updated content with relations
    const updatedContent = await db.getContentWithRelations(contentId);

    res.json({
      success: true,
      message: 'Content updated successfully',
      data: updatedContent
    });

  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update content'
    });
  }
});

// Delete content (admin/moderator only)
router.delete('/:id', authenticateToken, requireModerator, logAdminAction('content_delete'), async (req, res) => {
  try {
    const contentId = req.params.id;

    // Check if content exists
    const existingContent = await db.execute('SELECT title FROM content WHERE id = ?', [contentId]);
    if (existingContent.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // Delete content (cascade will handle related records)
    await db.execute('DELETE FROM content WHERE id = ?', [contentId]);

    res.json({
      success: true,
      message: 'Content deleted successfully',
      data: {
        id: contentId,
        title: existingContent[0].title
      }
    });

  } catch (error) {
    console.error('Error deleting content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete content'
    });
  }
});

// Bulk operations
router.post('/bulk', authenticateToken, requireModerator, logAdminAction('content_bulk_operation'), async (req, res) => {
  try {
    const { operation, contentIds, data } = req.body;

    if (!operation || !contentIds || !Array.isArray(contentIds)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Operation and contentIds array are required'
      });
    }

    let query;
    let params;
    let message;

    switch (operation) {
      case 'publish':
        query = 'UPDATE content SET is_published = 1, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content published successfully';
        break;

      case 'unpublish':
        query = 'UPDATE content SET is_published = 0, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content unpublished successfully';
        break;

      case 'feature':
        query = 'UPDATE content SET is_featured = 1, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content featured successfully';
        break;

      case 'unfeature':
        query = 'UPDATE content SET is_featured = 0, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content unfeatured successfully';
        break;

      case 'add_to_carousel':
        query = 'UPDATE content SET add_to_carousel = 1, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content added to carousel successfully';
        break;

      case 'remove_from_carousel':
        query = 'UPDATE content SET add_to_carousel = 0, updated_at = NOW() WHERE id IN (?)';
        params = [contentIds];
        message = 'Content removed from carousel successfully';
        break;

      case 'delete':
        query = 'DELETE FROM content WHERE id IN (?)';
        params = [contentIds];
        message = 'Content deleted successfully';
        break;

      case 'update_category':
        if (!data || !data.category_id) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Category ID is required for update_category operation'
          });
        }
        query = 'UPDATE content SET category_id = ?, updated_at = NOW() WHERE id IN (?)';
        params = [data.category_id, contentIds];
        message = 'Content category updated successfully';
        break;

      default:
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid operation'
        });
    }

    // Execute the bulk operation
    const result = await db.execute(query, params);

    res.json({
      success: true,
      message,
      data: {
        operation,
        affectedRows: result.affectedRows,
        contentIds
      }
    });

  } catch (error) {
    console.error('Error performing bulk operation:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to perform bulk operation'
    });
  }
});

module.exports = router;
