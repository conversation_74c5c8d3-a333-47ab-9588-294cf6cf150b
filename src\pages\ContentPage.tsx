import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Calendar, Clock, Star, Play, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import { mediaData } from '@/data/movies';
import { MediaItem } from '@/types/media';
import { scrollToTop } from '@/utils/scrollToTop';

export default function ContentPage() {
  const { id } = useParams<{ id: string }>();
  
  // Find the content item by ID
  const content: MediaItem | undefined = mediaData.find(item => item.id === id);

  if (!content) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-6xl mb-4">🎬</div>
            <h1 className="text-3xl font-bold mb-4 text-primary">Content Not Found</h1>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              The content you're looking for doesn't exist or may have been removed.
              Let's get you back to discovering great movies and series!
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                to="/"
                onClick={scrollToTop}
                className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Link>
              <Link
                to="/movies"
                onClick={scrollToTop}
                className="inline-flex items-center gap-2 px-6 py-3 border border-border rounded-md hover:bg-muted transition-colors"
              >
                Browse Movies
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Simulate secure video links (in production, this would come from the database)
  const hasVideoLinks = content.videoLinks || content.secureVideoLinks;

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <div className="relative h-[50vh] sm:h-[60vh] md:h-[70vh] min-h-[400px] sm:min-h-[450px] md:min-h-[500px] overflow-hidden">
          <img
            src={content.coverImage || content.image || '/placeholder-image.jpg'}
            alt={content.title || 'Content'}
            className="absolute inset-0 w-full h-full object-cover brightness-[0.35] scale-105 transition-transform duration-700 hover:scale-100"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/60 to-background/20" />
          <div className="absolute inset-0 bg-gradient-to-r from-background/40 via-transparent to-background/40" />

          <div className="relative z-10 h-full flex items-center sm:items-end">
            <div className="w-full max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8 md:pb-16">
              <div className="flex flex-col md:flex-row gap-6 sm:gap-8 md:gap-10 items-center md:items-end content-page-hero">
                {/* Poster - Fixed mobile bleeding */}
                <div className="flex-shrink-0 group mx-auto md:mx-0 content-page-poster-container">
                  <div className="relative">
                    <img
                      src={content.image}
                      alt={content.title}
                      className="content-page-poster w-36 h-56 sm:w-44 sm:h-68 md:w-52 md:h-80 object-cover rounded-xl sm:rounded-2xl shadow-2xl transition-all duration-500 group-hover:shadow-[0_25px_50px_rgba(230,203,142,0.3)] group-hover:scale-105"
                      style={{
                        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(230, 203, 142, 0.2)"
                      }}
                    />
                    <div className="absolute inset-0 rounded-xl sm:rounded-2xl bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </div>
                
                {/* Content Info */}
                <div className="flex-1 text-white text-center md:text-left">
                  <div className="mb-4">
                    <Badge
                      variant="secondary"
                      className="mb-3 bg-primary/20 text-primary border-primary/30 font-koulen uppercase tracking-wider text-xs sm:text-sm"
                    >
                      {content.type === 'movie' ? 'Movie' : 'Web Series'}
                    </Badge>
                    <h1 className="stdb-heading text-2xl sm:text-3xl md:text-4xl lg:text-6xl xl:text-7xl mb-3 leading-tight mobile-hero-title"
                        style={{
                          color: "#e6cb8e",
                          fontFamily: "'Koulen', Impact, Arial, sans-serif",
                          fontWeight: 400,
                          letterSpacing: "0.02em",
                          textTransform: "uppercase",
                          textShadow: "0 3px 20px rgba(230, 203, 142, 0.4), 0 2px 8px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.9)"
                        }}>
                      {content.title}
                    </h1>
                    <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-100 max-w-3xl leading-relaxed font-manrope mobile-hero-description mx-auto md:mx-0"
                       style={{ textShadow: "0 1px 3px rgba(0, 0, 0, 0.8)" }}>
                      {content.description}
                    </p>
                  </div>
                  
                  <div className="flex flex-wrap gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 justify-center md:justify-start">
                    <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                      <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                      <span className="font-medium text-sm sm:text-base">{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                        <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                        <span className="font-medium text-sm sm:text-base">{content.runtime} min</span>
                      </div>
                    )}
                    {content.imdbRating && (
                      <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-yellow-400" />
                        <span className="font-medium text-sm sm:text-base">{content.imdbRating}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-wrap gap-3 mb-8">
                    {content.genres.map((genre) => (
                      <Badge
                        key={genre}
                        variant="outline"
                        className="text-white border-primary/50 bg-primary/10 backdrop-blur-sm hover:bg-primary/20 hover:border-primary transition-all duration-300 font-medium px-3 py-1"
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex flex-wrap gap-4">
                    {hasVideoLinks && (
                      <Button
                        size="lg"
                        className="bg-primary hover:bg-primary/90 text-primary-foreground font-koulen uppercase tracking-wider px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      >
                        <Play className="w-5 h-5 mr-2 fill-current" />
                        Watch Now
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="lg"
                      className="text-white border-white/40 hover:bg-white/10 backdrop-blur-sm font-medium px-6 py-3 rounded-xl transition-all duration-300 hover:border-primary/50 hover:text-primary"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      className="text-white border-white/40 hover:bg-white/10 backdrop-blur-sm font-medium px-6 py-3 rounded-xl transition-all duration-300 hover:border-primary/50 hover:text-primary"
                    >
                      <Share2 className="w-5 h-5 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 py-6 sm:py-8 md:py-12 lg:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 lg:gap-10">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10">
              {/* Video Player */}
              {hasVideoLinks && (
                <Card className="content-page-card overflow-hidden rounded-xl sm:rounded-2xl border-border/50 shadow-2xl bg-card/95 backdrop-blur-sm">
                  <CardContent className="p-0">
                    <SecureVideoPlayer
                      encodedVideoLinks={content.secureVideoLinks}
                      legacyVideoLinks={content.videoLinks}
                      title={content.title}
                      showPlayerSelection={true}
                      className="w-full"
                    />
                  </CardContent>
                </Card>
              )}

              {/* Description */}
              <Card className="content-page-card rounded-xl sm:rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <h2 className="stdb-heading text-3xl mb-6"
                      style={{
                        color: "#e6cb8e",
                        fontFamily: "'Koulen', Impact, Arial, sans-serif",
                        fontWeight: 400,
                        letterSpacing: "0.05em",
                        textTransform: "uppercase",
                        textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                      }}>
                    About
                  </h2>
                  <p className="text-foreground/90 leading-relaxed text-lg font-manrope mb-6">{content.description}</p>

                  {content.tags && (
                    <div className="mt-8">
                      <h3 className="font-koulen uppercase tracking-wider text-primary mb-4 text-lg">Tags</h3>
                      <div className="flex flex-wrap gap-3">
                        {content.tags.split(',').map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors duration-200 px-3 py-1"
                          >
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Episodes (for series) */}
              {content.type === 'series' && content.seasons && content.seasons.length > 0 && (
                <Card className="content-page-card rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                  <CardContent className="p-8">
                    <h2 className="stdb-heading text-3xl mb-6"
                        style={{
                          color: "#e6cb8e",
                          fontFamily: "'Koulen', Impact, Arial, sans-serif",
                          fontWeight: 400,
                          letterSpacing: "0.05em",
                          textTransform: "uppercase",
                          textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                        }}>
                      Episodes
                    </h2>
                    <div className="space-y-6">
                      {content.seasons.map((season) => (
                        <div key={season.id}>
                          <h3 className="text-xl font-koulen uppercase tracking-wider text-primary mb-4">
                            Season {season.seasonNumber}
                          </h3>
                          <div className="grid gap-3">
                            {season.episodes.map((episode) => (
                              <div
                                key={episode.id}
                                className="flex items-center justify-between p-4 border border-border/50 rounded-xl hover:bg-muted/30 hover:border-primary/30 transition-all duration-300 group"
                              >
                                <div className="flex-1">
                                  <span className="font-medium text-foreground group-hover:text-primary transition-colors">
                                    E{episode.episode}: {episode.title}
                                  </span>
                                  {episode.description && (
                                    <p className="text-sm text-muted-foreground mt-1 leading-relaxed">
                                      {episode.description}
                                    </p>
                                  )}
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="ml-4 hover:bg-primary/20 hover:text-primary transition-all duration-200"
                                >
                                  <Play className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-4 sm:space-y-6 lg:space-y-8">
              {/* Details */}
              <Card className="content-page-card rounded-xl sm:rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <h3 className="stdb-heading text-2xl mb-6"
                      style={{
                        color: "#e6cb8e",
                        fontFamily: "'Koulen', Impact, Arial, sans-serif",
                        fontWeight: 400,
                        letterSpacing: "0.05em",
                        textTransform: "uppercase",
                        textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                      }}>
                    Details
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-border/30">
                      <span className="text-muted-foreground font-medium">Type</span>
                      <span className="capitalize font-semibold text-primary">{content.type}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-border/30">
                      <span className="text-muted-foreground font-medium">Year</span>
                      <span className="font-semibold">{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Runtime</span>
                        <span className="font-semibold">{content.runtime} minutes</span>
                      </div>
                    )}
                    {content.studio && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Studio</span>
                        <span className="font-semibold text-right max-w-[60%]">{content.studio}</span>
                      </div>
                    )}
                    {content.languages && content.languages.length > 0 && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Languages</span>
                        <span className="font-semibold text-right max-w-[60%]">{content.languages.join(', ')}</span>
                      </div>
                    )}
                    {content.quality && content.quality.length > 0 && (
                      <div className="flex justify-between items-center py-2">
                        <span className="text-muted-foreground font-medium">Quality</span>
                        <div className="flex flex-wrap gap-1 justify-end max-w-[60%]">
                          {content.quality.map((q, index) => (
                            <Badge key={index} variant="secondary" className="bg-primary/10 text-primary border-primary/30 text-xs">
                              {q}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Back Button */}
              <Button
                variant="outline"
                className="w-full rounded-xl border-border/50 hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-300 py-3 font-medium"
                onClick={() => {
                  window.history.back();
                  scrollToTop();
                }}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
